import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const TimelineWrapper = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 0;
`;

const TimelineItem = styled(motion.div)`
  display: flex;
  margin: 2rem 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    height: 100%;
    width: 2px;
    background: #1a73e8;
    opacity: 0.3;
  }

  &:last-child::before {
    display: none;
  }
`;

const Dot = styled(motion.div)`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: #1a73e8;
  margin: 0.5rem 1rem;
  flex-shrink: 0;
`;

const Content = styled(motion.div)`
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;

  h3 {
    color: #1a73e8;
    margin-bottom: 0.5rem;
  }

  .company {
    font-weight: 500;
    color: #444;
    margin-bottom: 0.5rem;
  }

  .date {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  ul {
    margin: 0;
    padding-left: 1.2rem;

    li {
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }
  }
`;

const ExperienceTimeline = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const dotVariants = {
    hidden: { scale: 0 },
    visible: {
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const experiences = [
    {
      title: "Data Science Intern",
      company: "BrainChain Pvt. Ltd.",
      date: "April 2025 – June 2025",
      location: "Pune, MH, IN",
      achievements: [
        "Led data extraction project processing 7,000+ records using Python, BeautifulSoup, and Selenium",
        "Reduced manual monitoring time by 80% through custom Telegram Bot implementation",
        "Developed automated email classification system for stakeholder updates",
        "Contributed to AI/ML pipeline optimization and data preprocessing workflows"
      ]
    },
    {
      title: "Junior Python Developer",
      company: "Oytie Pvt. Ltd.",
      date: "December 2023 – January 2024",
      location: "Pune, MH, IN",
      achievements: [
        "Architected and implemented 8+ scalable Django backend modules",
        "Optimized MySQL database schemas reducing query latency by 40%",
        "Served 500+ active users across multiple departments",
        "Implemented robust error handling and logging systems"
      ]
    },
    {
      title: "Artificial Intelligence Intern",
      company: "RadicalX",
      date: "October 2023 – November 2023",
      location: "Remote, Haryana, IN",
      achievements: [
        "Contributed to ML model experimentation using scikit-learn, TensorFlow, and OpenCV",
        "Collaborated with research teams on AI prototype development",
        "Gained hands-on experience with model training workflows and evaluation metrics",
        "Participated in regular code reviews and documentation improvements"
      ]
    }
  ];

  return (
    <TimelineWrapper
      ref={ref}
      as={motion.div}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
    >
      {experiences.map((exp, index) => (
        <TimelineItem key={index} variants={itemVariants}>
          <Dot variants={dotVariants} />
          <Content>
            <h3>{exp.title}</h3>
            <div className="company">{exp.company}</div>
            <div className="date">{exp.date} | {exp.location}</div>
            <ul>
              {exp.achievements.map((achievement, i) => (
                <li key={i}>{achievement}</li>
              ))}
            </ul>
          </Content>
        </TimelineItem>
      ))}
    </TimelineWrapper>
  );
};

export default ExperienceTimeline;
