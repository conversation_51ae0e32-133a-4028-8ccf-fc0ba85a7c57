import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const SkillsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
`;

const SkillCategory = styled(motion.div)`
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const CategoryTitle = styled.h3`
  color: #1a73e8;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
`;

const SkillBar = styled(motion.div)`
  margin-bottom: 1.2rem;

  .skill-name {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .progress-bar {
    height: 8px;
    background: #e8f0fe;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: #1a73e8;
    border-radius: 4px;
    transform-origin: left;
  }
`;

const skills = {
  "Programming & Data Science": [
    { name: "Python", level: 95 },
    { name: "SQL", level: 90 },
    { name: "Machine Learning", level: 85 },
    { name: "Deep Learning", level: 80 },
    { name: "Data Analysis", level: 90 }
  ],
  "Frameworks & Tools": [
    { name: "TensorFlow", level: 85 },
    { name: "PyTorch", level: 80 },
    { name: "Scikit-learn", level: 90 },
    { name: "FastAPI", level: 85 },
    { name: "Django", level: 80 }
  ],
  "Cloud & DevOps": [
    { name: "Git/GitHub", level: 90 },
    { name: "Docker", level: 75 },
    { name: "CI/CD", level: 70 },
    { name: "AWS", level: 75 },
    { name: "MLOps", level: 75 }
  ],
  "Soft Skills": [
    { name: "Problem Solving", level: 95 },
    { name: "Team Collaboration", level: 90 },
    { name: "Communication", level: 85 },
    { name: "Project Management", level: 80 },
    { name: "Leadership", level: 85 }
  ]
};

const SkillsSection = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const categoryVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  };

  const barVariants = {
    hidden: { scaleX: 0 },
    visible: {
      scaleX: 1,
      transition: {
        duration: 0.8,
        ease: "easeInOut"
      }
    }
  };

  return (
    <SkillsContainer
      ref={ref}
      as={motion.div}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
    >
      {Object.entries(skills).map(([category, skillList]) => (
        <SkillCategory key={category} variants={categoryVariants}>
          <CategoryTitle>{category}</CategoryTitle>
          {skillList.map((skill) => (
            <SkillBar key={skill.name}>
              <div className="skill-name">
                <span>{skill.name}</span>
                <span>{skill.level}%</span>
              </div>
              <div className="progress-bar">
                <motion.div
                  className="progress-fill"
                  variants={barVariants}
                  style={{ width: `${skill.level}%` }}
                />
              </div>
            </SkillBar>
          ))}
        </SkillCategory>
      ))}
    </SkillsContainer>
  );
};

export default SkillsSection;
