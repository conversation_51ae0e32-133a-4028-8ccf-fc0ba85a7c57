import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const ProjectCard = styled(motion.div)`
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
`;

const ProjectTitle = styled.h3`
  font-size: 1.4rem;
  color: #1a73e8;
  margin-bottom: 1rem;
`;

const Description = styled.p`
  color: #555;
  margin-bottom: 1rem;
  line-height: 1.6;
`;

const TechTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const Tag = styled.span`
  background: #e8f0fe;
  color: #1967d2;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
`;

const Links = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

const Link = styled.a`
  color: #1a73e8;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
`;

const Metrics = styled.div`
  display: flex;
  gap: 2rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
`;

const Metric = styled.div`
  text-align: center;

  .value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #1a73e8;
  }

  .label {
    font-size: 0.9rem;
    color: #666;
  }
`;

const projectData = [
  {
    title: "Movie Recommender",
    description: "Built a content-based movie recommendation engine achieving 85% relevance accuracy. Integrated TMDB API for rich movie metadata and created an engaging Streamlit UI for seamless user interaction.",
    tech: ["Python", "Scikit-learn", "TMDB API", "Streamlit", "NLP"],
    metrics: [
      { value: "85%", label: "Accuracy" },
      { value: "5K+", label: "Movies" },
      { value: "30%", label: "User Engagement" }
    ],
    live: "https://new-movie-recommender-system.streamlit.app/",
    github: "https://github.com/nilkanth02/movie-recommender"
  },
  {
    title: "FunStrings Python Package",
    description: "Published a PyPI package with 44+ string processing utilities across 8 categories. Downloaded 1,000+ times, helping developers accelerate ML/NLP text workflows by up to 30%.",
    tech: ["Python", "PyPI", "NLP", "Text Processing", "Unit Testing"],
    metrics: [
      { value: "1K+", label: "Downloads" },
      { value: "44+", label: "Utilities" },
      { value: "30%", label: "Speed Boost" }
    ],
    live: "https://fun-string-website.vercel.app/",
    github: "https://github.com/nilkanth02/funstrings"
  },
  {
    title: "Student Placement Prediction",
    description: "Developed an ML model to predict student placement outcomes with high accuracy. Implemented a full-stack solution with data preprocessing, model training, and API deployment.",
    tech: ["Python", "Scikit-learn", "FastAPI", "React", "PostgreSQL"],
    metrics: [
      { value: "92%", label: "Accuracy" },
      { value: "1K+", label: "Predictions" },
      { value: "4", label: "ML Models" }
    ],
    live: "https://students-placement-project.onrender.com/",
    github: "https://github.com/nilkanth02/placement-prediction"
  }
];

const ProjectShowcase = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
    >
      {projectData.map((project, index) => (
        <ProjectCard key={index} variants={cardVariants}>
          <ProjectTitle>{project.title}</ProjectTitle>
          <Description>{project.description}</Description>
          
          <TechTags>
            {project.tech.map((tech, i) => (
              <Tag key={i}>{tech}</Tag>
            ))}
          </TechTags>

          <Metrics>
            {project.metrics.map((metric, i) => (
              <Metric key={i}>
                <div className="value">{metric.value}</div>
                <div className="label">{metric.label}</div>
              </Metric>
            ))}
          </Metrics>

          <Links>
            <Link href={project.live} target="_blank" rel="noopener noreferrer">
              🚀 Live Demo
            </Link>
            {project.github && (
              <Link href={project.github} target="_blank" rel="noopener noreferrer">
                💻 GitHub
              </Link>
            )}
          </Links>
        </ProjectCard>
      ))}
    </motion.div>
  );
};

export default ProjectShowcase;
