import styled from 'styled-components';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>React, DiMongodb } from 'react-icons/di';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Openai } from 'react-icons/si';

const StyledHero = styled(motion.div)`
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: linear-gradient(135deg, #1a73e8 0%, #0d47a1 100%);
  color: white;
  padding: 2rem;
  position: relative;
  overflow: hidden;
`;

const Title = styled(motion.h1)`
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-weight: bold;
`;

const Subtitle = styled(motion.h2)`
  font-size: 1.8rem;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const Stats = styled.div`
  display: flex;
  gap: 3rem;
  margin: 2rem 0;
`;

const StatItem = styled(motion.div)`
  text-align: center;
  
  .number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #fff;
    margin-bottom: 0.5rem;
  }
  
  .label {
    font-size: 1rem;
    opacity: 0.9;
  }
`;

const TechStack = styled.div`
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
`;

const TechIcon = styled(motion.div)`
  font-size: 2.5rem;
  color: white;
  opacity: 0.9;
  cursor: pointer;
  
  &:hover {
    opacity: 1;
    transform: translateY(-5px);
  }
`;

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { staggerChildren: 0.3 }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  const stats = [
    { number: "10+", label: "Projects Deployed" },
    { number: "7K+", label: "Data Records Processed" },
    { number: "80%", label: "Task Automation" },
    { number: "3", label: "Industry Internships" }
  ];

  return (
    <StyledHero
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Title variants={itemVariants}>
        Nilkanth Ahire
      </Title>
      <Subtitle variants={itemVariants}>
        Data Science | Machine Learning | Full Stack Development
      </Subtitle>
      
      <motion.div variants={itemVariants}>
        <p style={{ fontSize: '1.2rem', maxWidth: '800px', margin: '0 auto' }}>
          Transforming data into actionable insights and building intelligent solutions
          that drive business value
        </p>
      </motion.div>

      <Stats>
        {stats.map((stat, index) => (
          <StatItem
            key={index}
            variants={itemVariants}
            whileHover={{ scale: 1.1 }}
          >
            <div className="number">{stat.number}</div>
            <div className="label">{stat.label}</div>
          </StatItem>
        ))}
      </Stats>

      <TechStack>
        <TechIcon
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <DiPython title="Python" />
        </TechIcon>
        <TechIcon
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <SiTensorflow title="TensorFlow" />
        </TechIcon>
        <TechIcon
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <SiScikitlearn title="Scikit-learn" />
        </TechIcon>
        <TechIcon
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <DiReact title="React" />
        </TechIcon>
        <TechIcon
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <DiMongodb title="MongoDB" />
        </TechIcon>
        <TechIcon
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <SiOpenai title="AI/ML" />
        </TechIcon>
      </TechStack>
    </StyledHero>
  );
};

export default Hero;
