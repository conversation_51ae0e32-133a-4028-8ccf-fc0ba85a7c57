
import styled from 'styled-components';
import Hero from './components/Hero';
import ProjectShowcase from './components/ProjectShowcase';
import ExperienceTimeline from './components/ExperienceTimeline';
import SkillsSection from './components/SkillsSection';

const MainContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
`;

const MainSection = styled.section`
  padding: 4rem 0;
`;

const MainTitle = styled.h2`
  font-size: 2rem;
  color: #1a73e8;
  margin-bottom: 2rem;
  text-align: center;
`;

function App() {
  return (
    <>
      <Hero />
      <MainContainer>
        <MainSection>
          <MainTitle>Featured Projects</MainTitle>
          <ProjectShowcase />
        </MainSection>

        <MainSection>
          <MainTitle>Professional Experience</MainTitle>
          <ExperienceTimeline />
        </MainSection>

        <MainSection>
          <MainTitle>Skills & Expertise</MainTitle>
          <SkillsSection />
        </MainSection>
      </MainContainer>
    </>
  );
}

export default App;
